import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import '../services/analytics_service.dart';
import '../theme/theme.dart';
import '../widgets/persona_card.dart';
import '../widgets/persona_video_modal.dart';
import '../widgets/persona_selection_carousel.dart';
import 'chat_page.dart';

class PersonaSelectionPage extends StatefulWidget {
  const PersonaSelectionPage({super.key});

  @override
  State<PersonaSelectionPage> createState() => _PersonaSelectionPageState();
}

class _PersonaSelectionPageState extends State<PersonaSelectionPage> {
  List<models.SystemPersona> _systemPersonas = [];
  List<models.SystemPersona> _orderedPersonas = [];
  String? _selectedPersonaId;
  bool _isLoading = true;
  String? _errorMessage;
  models.User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Load user data and system personas in parallel
      final results = await Future.wait([
        FirestoreService.getUser(currentUser.uid),
        FirestoreService.getActiveSystemPersonas(),
      ]);

      _currentUser = results[0] as models.User?;
      _systemPersonas = results[1] as List<models.SystemPersona>;

      // Order personas: preferred first, then remaining
      _orderPersonas();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load personas: $e';
        _isLoading = false;
      });
    }
  }

  void _orderPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    final preferredPersonas = <models.SystemPersona>[];
    final remainingPersonas = <models.SystemPersona>[];

    for (final persona in _systemPersonas) {
      if (persona.id != null && preferredIds.contains(persona.id)) {
        preferredPersonas.add(persona);
      } else {
        remainingPersonas.add(persona);
      }
    }

    // Sort preferred personas by their order in preferredPersonaIds
    preferredPersonas.sort((a, b) {
      final aIndex = preferredIds.indexOf(a.id!);
      final bIndex = preferredIds.indexOf(b.id!);
      return aIndex.compareTo(bIndex);
    });

    _orderedPersonas = [...preferredPersonas, ...remainingPersonas];
  }

  void _selectPersona(String? personaId) {
    setState(() {
      _selectedPersonaId = personaId;
    });
  }

  Future<void> _startChat() async {
    if (_selectedPersonaId == null) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Find the selected persona for analytics
      final selectedPersona = _systemPersonas.firstWhere(
        (persona) => persona.id == _selectedPersonaId,
      );

      // Track persona selection in analytics
      await AnalyticsService.instance.logPersonaSelected(
        personaId: _selectedPersonaId!,
        personaName: selectedPersona.name,
        selectionContext: 'chat_creation',
      );

      // Create new chat with selected persona (we know it's not null due to check above)
      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: _selectedPersonaId!,
      );

      // Track chat creation in analytics
      await AnalyticsService.instance.logChatCreated(
        personaId: _selectedPersonaId!,
        personaName: selectedPersona.name,
      );

      // Fetch the created chat object
      final chat = await FirestoreService.getChat(currentUser.uid, chatId);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Navigate to ChatPage with the chat object
      if (mounted && chat != null) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => ChatPage(chat: chat)),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load chat data')),
        );
      }
    } catch (e) {
      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to start chat: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Coach'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(),
      floatingActionButton: _selectedPersonaId != null
          ? Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: FloatingActionButton.extended(
                onPressed: _startChat,
                label: const Text('Start Chat'),
                icon: const Icon(Icons.chat),
              ),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.error,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _errorMessage!,
              style: context.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(onPressed: _loadData, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_orderedPersonas.isEmpty) {
      return const Center(child: Text('No coaches available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header text
          Text(
            'Choose Your AI Coach',
            style: context.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.primary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Swipe through our collection of AI coaching personalities. Tap on any avatar to watch their introduction video.',
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Preferred personas carousel
          if (_currentUser?.preferredPersonaIds.isNotEmpty == true) ...[
            Text(
              'Your Preferred Coaches',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: context.colorScheme.primary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            PersonaSelectionCarousel(
              personas: _getPreferredPersonas(),
              selectedPersonaId: _selectedPersonaId,
              onPersonaSelected: _selectPersona,
              onAvatarTap: _showPersonaVideo,
              showSelectionIndicator: true,
            ),
            SizedBox(height: AppDimensions.spacingL),

            if (_getRemainingPersonas().isNotEmpty) ...[
              Text(
                'Other Coaches',
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: AppDimensions.spacingM),
              PersonaSelectionCarousel(
                personas: _getRemainingPersonas(),
                selectedPersonaId: _selectedPersonaId,
                onPersonaSelected: _selectPersona,
                onAvatarTap: _showPersonaVideo,
                showSelectionIndicator: true,
              ),
            ],
          ] else ...[
            // All personas carousel when no preferred personas
            PersonaSelectionCarousel(
              personas: _orderedPersonas,
              selectedPersonaId: _selectedPersonaId,
              onPersonaSelected: _selectPersona,
              onAvatarTap: _showPersonaVideo,
              showSelectionIndicator: true,
            ),
          ],
        ],
      ),
    );
  }

  List<models.SystemPersona> _getPreferredPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    return _orderedPersonas
        .where(
          (persona) => persona.id != null && preferredIds.contains(persona.id),
        )
        .toList();
  }

  List<models.SystemPersona> _getRemainingPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    return _orderedPersonas
        .where(
          (persona) => persona.id == null || !preferredIds.contains(persona.id),
        )
        .toList();
  }

  void _showPersonaVideo(models.SystemPersona persona) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/coach.mp4',
    );
  }
}
